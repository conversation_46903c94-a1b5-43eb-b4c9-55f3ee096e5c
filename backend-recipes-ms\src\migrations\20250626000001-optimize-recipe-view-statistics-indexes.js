'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      console.log('🚀 Adding optimized indexes for recipe view statistics performance...');

      // 1. Optimize mo_recipe_analytics table for recipe view queries
      await queryInterface.addIndex(
        'mo_recipe_analytics',
        {
          fields: ['entity_id', 'entity_type', 'event_type', 'user_id'],
          name: 'idx_analytics_recipe_view_lookup',
          transaction
        }
      );

      // 2. Add composite index for analytics created_at queries
      await queryInterface.addIndex(
        'mo_recipe_analytics',
        {
          fields: ['entity_id', 'event_type', 'user_id', 'created_at'],
          name: 'idx_analytics_recipe_view_time',
          transaction
        }
      );

      // 3. Optimize mo_recipe table for recipe status and organization queries
      await queryInterface.addIndex(
        'mo_recipe',
        {
          fields: ['id', 'recipe_status', 'has_recipe_private_visibility', 'organization_id'],
          name: 'idx_recipe_view_stats_lookup',
          transaction
        }
      );

      // 4. Optimize mo_recipe_user table for active user lookups
      await queryInterface.addIndex(
        'mo_recipe_user',
        {
          fields: ['recipe_id', 'status', 'user_id'],
          name: 'idx_recipe_user_active_lookup',
          transaction
        }
      );

      // 5. Optimize nv_users table for user status and profile queries
      await queryInterface.addIndex(
        'nv_users',
        {
          fields: ['id', 'user_status', 'branch_id', 'department_id'],
          name: 'idx_users_profile_lookup',
          transaction
        }
      );

      // 6. Add index for nv_branches table if it doesn't exist
      try {
        await queryInterface.addIndex(
          'nv_branches',
          {
            fields: ['id'],
            name: 'idx_branches_id',
            transaction
          }
        );
      } catch (error) {
        // Index might already exist, continue
        console.log('Branch index might already exist, continuing...');
      }

      // 7. Add index for nv_departments table if it doesn't exist
      try {
        await queryInterface.addIndex(
          'nv_departments',
          {
            fields: ['id'],
            name: 'idx_departments_id',
            transaction
          }
        );
      } catch (error) {
        // Index might already exist, continue
        console.log('Department index might already exist, continuing...');
      }

      // 8. Add index for nv_items table for avatar lookups
      try {
        await queryInterface.addIndex(
          'nv_items',
          {
            fields: ['id', 'item_location'],
            name: 'idx_items_avatar_lookup',
            transaction
          }
        );
      } catch (error) {
        // Index might already exist, continue
        console.log('Items index might already exist, continuing...');
      }

      await transaction.commit();
      console.log('✅ Successfully added optimized indexes for recipe view statistics');
      console.log('📊 Expected performance improvement: Query time should be under 75ms');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error adding optimized indexes:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      console.log('🔄 Removing optimized indexes for recipe view statistics...');

      // Remove all the indexes we added
      const indexesToRemove = [
        { table: 'mo_recipe_analytics', name: 'idx_analytics_recipe_view_lookup' },
        { table: 'mo_recipe_analytics', name: 'idx_analytics_recipe_view_time' },
        { table: 'mo_recipe', name: 'idx_recipe_view_stats_lookup' },
        { table: 'mo_recipe_user', name: 'idx_recipe_user_active_lookup' },
        { table: 'nv_users', name: 'idx_users_profile_lookup' },
        { table: 'nv_branches', name: 'idx_branches_id' },
        { table: 'nv_departments', name: 'idx_departments_id' },
        { table: 'nv_items', name: 'idx_items_avatar_lookup' }
      ];

      for (const index of indexesToRemove) {
        try {
          await queryInterface.removeIndex(index.table, index.name, { transaction });
        } catch (error) {
          // Index might not exist, continue with others
          console.log(`Index ${index.name} might not exist, continuing...`);
        }
      }

      await transaction.commit();
      console.log('✅ Successfully removed optimized indexes');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error removing optimized indexes:', error);
      throw error;
    }
  }
};
